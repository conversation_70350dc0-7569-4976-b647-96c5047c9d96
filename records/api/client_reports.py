import os
from fastapi import APIRouter


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'reports'))

    router.add_api_route("/download_main_report", download_main_report, methods=['GET'], name='Download main report')
    # router.add_api_route("", list_client_reports, methods=['GET'], name='List client reports')
    # router.add_api_route("", create_client_report, methods=['POST'], name='Create client report')
    # router.add_api_route("/{report_id}", get_client_report, methods=['GET'], name='Get client report')
    # router.add_api_route("/{report_id}", update_client_report, methods=['PUT'], name='Update client report')
    # router.add_api_route("/{report_id}", delete_client_report, methods=['DELETE'], name='Delete client report')

    return router


async def download_main_report(client_id: str, format='xlsx'):
    pass


async def list_client_reports():
    pass


async def create_client_report():
    pass


async def get_client_report():
    pass


async def update_client_report():
    pass


async def delete_client_report():
    pass
