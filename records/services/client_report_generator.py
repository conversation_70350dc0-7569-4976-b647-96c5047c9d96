import logging
import io
import datetime
from typing import Dict, <PERSON>, Tuple, Optional, Any
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, Ali<PERSON>ment, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows

from records.db import api as db_api
from records.utils import utils

logger = logging.getLogger(__name__)


class ClientReportGenerator:
    """
    Service for generating client reports in various formats.
    Supports generating questionnaire-style reports similar to the reference Excel file.
    """
    
    def __init__(self):
        self.questionnaire_template = self._get_questionnaire_template()
    
    def _get_questionnaire_template(self) -> List[Dict[str, Any]]:
        """
        Define the questionnaire template structure based on the reference Excel file.
        Each item represents a row in the questionnaire with field mapping information.
        """
        return [
            # I. Corporation INFO
            {"section": "I. Corporation INFO", "field": "Company Name", "db_field": "name", "required": True},
            {"section": "I. Corporation INFO", "field": "Tax ID/EIN", "db_field": "ein", "required": True},
            {"section": "I. Corporation INFO", "field": "Type of business /description", "db_field": "description", "required": False},
            {"section": "I. Corporation INFO", "field": "NAICS code (choose here https://www.naics.com/search/)", "db_field": "naicscode", "required": False},
            {"section": "I. Corporation INFO", "field": "Primary State of registration", "db_field": "primary_registration.state_of_incorporation", "required": False},
            {"section": "I. Corporation INFO", "field": "State Registration Number", "db_field": "primary_registration.state_entity", "required": False},
            {"section": "I. Corporation INFO", "field": "Date of registration", "db_field": "primary_registration.registered_date", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Is corporation registered in additional state? (YES or NO)", "db_field": "secondary_registrations", "required": False, "transform": "has_secondary_registration"},
            {"section": "I. Corporation INFO", "field": "Secondary State of Registration", "db_field": "secondary_registrations.0.state_of_incorporation", "required": False},
            {"section": "I. Corporation INFO", "field": "Secondary State Registration number", "db_field": "secondary_registrations.0.state_entity", "required": False},
            {"section": "I. Corporation INFO", "field": "Secondary state registration Date", "db_field": "secondary_registrations.0.registered_date", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Accounting method - CASH or ACCRUAL", "db_field": "accounting_method", "required": False},
            {"section": "I. Corporation INFO", "field": "FYE (Financial Year End)", "db_field": "financial_year_end", "required": False},
            {"section": "I. Corporation INFO", "field": "Mailing address in US (in the state of registration)", "db_field": "addresses", "required": False, "transform": "mailing_address"},
            {"section": "I. Corporation INFO", "field": "Telephone in US:", "db_field": "company_phone", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "SUBSIDIARY / Дочернее предприятие", "db_field": "subsidiary_to_consolidate", "required": False},
            {"section": "I. Corporation INFO", "field": "Jurisdiction of subsidiary", "db_field": "subjurisd", "required": False},
            {"section": "I. Corporation INFO", "field": "Legal Ent.Type of Subsidiary", "db_field": "subsidiary_legal_entity_type", "required": False},
            {"section": "I. Corporation INFO", "field": "FYE for subsidiary:", "db_field": "financial_year_end_for_subsidiary", "required": False},
            {"section": "", "field": "", "db_field": "", "required": False},  # Empty row
            {"section": "I. Corporation INFO", "field": "Total NUMBER OF Authorized shares (from Certificate of Incorporation)", "db_field": "shares", "required": False, "transform": "total_authorized_shares"},
            {"section": "I. Corporation INFO", "field": "Total NUMBER OF ACTUAL ISSUED SHARES (excluding SAFE/Options)", "db_field": "shares", "required": False, "transform": "total_issued_shares"},
            {"section": "I. Corporation INFO", "field": "Total Assets as of last day of Tax Year?", "db_field": "tax_reports", "required": False, "transform": "total_assets"},
            {"section": "I. Corporation INFO", "field": "", "db_field": "shareholders", "required": False, "transform": "shareholder_details"},
            {"section": "I. Corporation INFO", "field": "Did you file your Prior year tax returns?", "db_field": "tax_reports", "required": False, "transform": "filed_prior_returns"},
            {"section": "I. Corporation INFO", "field": "Have WE prepared your Prior year tax returns?", "db_field": "tax_reports", "required": False, "transform": "we_prepared_returns"},
            {"section": "I. Corporation INFO", "field": "Did company file 1099 forms to US based contractors?", "db_field": "fedtaxforms", "required": False, "transform": "filed_1099"},
            {"section": "I. Corporation INFO", "field": "If \"Yes\", How many?", "db_field": "", "required": False},
            {"section": "I. Corporation INFO", "field": "Did company have employees on payroll this year?", "db_field": "payroll", "required": False, "transform": "yes_no"},
            {"section": "I. Corporation INFO", "field": "If \"Yes\", How many?", "db_field": "", "required": False},
        ]
    
    async def generate_questionnaire_excel(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style Excel report for the specified client.
        
        Args:
            client_id: The ID of the client to generate the report for
            
        Returns:
            Tuple of (report_data_bytes, filename)
        """
        try:
            # Get comprehensive client data
            client_data = await self._get_comprehensive_client_data(client_id)
            
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Info"
            
            # Set up styles
            header_font = Font(bold=True, size=12)
            normal_font = Font(size=11)
            
            # Generate questionnaire data
            questionnaire_data = await self._generate_questionnaire_data(client_data)
            
            # Write data to worksheet
            for row_idx, (question, answer) in enumerate(questionnaire_data, start=1):
                # Column A: Question
                cell_a = ws.cell(row=row_idx, column=1, value=question)
                cell_a.font = header_font if question and not question.startswith(" ") else normal_font
                
                # Column B: Answer
                cell_b = ws.cell(row=row_idx, column=2, value=answer)
                cell_b.font = normal_font
                
                # Adjust row height for readability
                ws.row_dimensions[row_idx].height = 20
            
            # Adjust column widths
            ws.column_dimensions['A'].width = 60
            ws.column_dimensions['B'].width = 80
            
            # Save to bytes
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            
            # Generate filename
            client_name = client_data.get('name', 'Unknown').replace(' ', '_')
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d')
            filename = f"Questionnaire_CORP_{timestamp}_{client_name}.xlsx"
            
            return output.getvalue(), filename
            
        except Exception as e:
            logger.error(f"Error generating Excel questionnaire for client {client_id}: {str(e)}")
            raise
    
    async def generate_questionnaire_csv(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style CSV report for the specified client.
        
        Args:
            client_id: The ID of the client to generate the report for
            
        Returns:
            Tuple of (report_data_bytes, filename)
        """
        try:
            # Get comprehensive client data
            client_data = await self._get_comprehensive_client_data(client_id)
            
            # Generate questionnaire data
            questionnaire_data = await self._generate_questionnaire_data(client_data)
            
            # Create DataFrame
            df = pd.DataFrame(questionnaire_data, columns=['Question', 'Answer'])
            
            # Save to CSV
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8')
            
            # Convert to bytes
            csv_bytes = output.getvalue().encode('utf-8')
            
            # Generate filename
            client_name = client_data.get('name', 'Unknown').replace(' ', '_')
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d')
            filename = f"Questionnaire_CORP_{timestamp}_{client_name}.csv"
            
            return csv_bytes, filename
            
        except Exception as e:
            logger.error(f"Error generating CSV questionnaire for client {client_id}: {str(e)}")
            raise
    
    async def generate_questionnaire_pdf(self, client_id: str) -> Tuple[bytes, str]:
        """
        Generate a questionnaire-style PDF report for the specified client.
        
        Args:
            client_id: The ID of the client to generate the report for
            
        Returns:
            Tuple of (report_data_bytes, filename)
        """
        # For now, we'll implement a basic PDF generation
        # In a production environment, you might want to use libraries like reportlab or weasyprint
        raise NotImplementedError("PDF generation not yet implemented. Please use Excel or CSV format.")
    
    async def _get_comprehensive_client_data(self, client_id: str) -> Dict[str, Any]:
        """
        Retrieve comprehensive client data including all related entities.
        
        Args:
            client_id: The ID of the client
            
        Returns:
            Dictionary containing all client data
        """
        # Get main client data
        client = await db_api.get_client_by_id(client_id)
        if not client:
            raise ValueError(f"Client with ID {client_id} not found")
        
        client_dict = client.to_dict()
        
        # Get related data
        client_dict['addresses'] = await self._get_client_addresses(client_id)
        client_dict['contacts'] = await self._get_client_contacts(client_id)
        client_dict['shareholders'] = await self._get_client_shareholders(client_id)
        client_dict['bank_accounts'] = await self._get_client_bank_accounts(client_id)
        client_dict['tax_reports'] = await self._get_client_tax_reports(client_id)
        client_dict['shares'] = await self._get_client_shares(client_id)
        client_dict['primary_registration'] = await self._get_primary_registration(client_id)
        client_dict['secondary_registrations'] = await self._get_secondary_registrations(client_id)
        
        return client_dict
    
    async def _get_client_addresses(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client addresses with full address details."""
        try:
            addresses = await db_api.list_client_addresses(client_id)
            return [addr.to_dict() for addr in addresses]
        except Exception as e:
            logger.warning(f"Error getting addresses for client {client_id}: {str(e)}")
            return []
    
    async def _get_client_contacts(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client contacts with person details."""
        try:
            contacts = await db_api.list_client_contacts(client_id)
            return [contact.to_dict() for contact in contacts]
        except Exception as e:
            logger.warning(f"Error getting contacts for client {client_id}: {str(e)}")
            return []
    
    async def _get_client_shareholders(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client shareholders with person details."""
        try:
            shareholders = await db_api.list_client_shareholders(client_id)
            return [shareholder.to_dict() for shareholder in shareholders]
        except Exception as e:
            logger.warning(f"Error getting shareholders for client {client_id}: {str(e)}")
            return []
    
    async def _get_client_bank_accounts(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client bank accounts."""
        try:
            bank_accounts = await db_api.list_bank_accounts(client_id)
            return [account.to_dict() for account in bank_accounts]
        except Exception as e:
            logger.warning(f"Error getting bank accounts for client {client_id}: {str(e)}")
            return []

    async def _get_client_tax_reports(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client tax reports."""
        try:
            tax_reports = await db_api.list_client_tax_reports(client_id)
            return [report.to_dict() for report in tax_reports]
        except Exception as e:
            logger.warning(f"Error getting tax reports for client {client_id}: {str(e)}")
            return []

    async def _get_client_shares(self, client_id: str) -> List[Dict[str, Any]]:
        """Get client share information."""
        try:
            shares = await db_api.list_client_shares(client_id)
            return [share.to_dict() for share in shares]
        except Exception as e:
            logger.warning(f"Error getting shares for client {client_id}: {str(e)}")
            return []

    async def _get_primary_registration(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get primary registration information."""
        try:
            registration = await db_api.get_primary_client_registration(client_id)
            return registration.to_dict() if registration else None
        except Exception as e:
            logger.warning(f"Error getting primary registration for client {client_id}: {str(e)}")
            return None

    async def _get_secondary_registrations(self, client_id: str) -> List[Dict[str, Any]]:
        """Get secondary registration information."""
        try:
            # Get all registrations and filter out the primary one
            registrations = await db_api.list_client_registrations(client_id, all=True)
            secondary_regs = [reg for reg in registrations if not reg.is_primary]
            return [reg.to_dict() for reg in secondary_regs]
        except Exception as e:
            logger.warning(f"Error getting secondary registrations for client {client_id}: {str(e)}")
            return []

    async def _generate_questionnaire_data(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """
        Generate questionnaire data by mapping client data to template fields.

        Args:
            client_data: Comprehensive client data dictionary

        Returns:
            List of (question, answer) tuples
        """
        questionnaire_data = []

        # Process each template item
        for template_item in self.questionnaire_template:
            question = template_item['field']
            db_field = template_item['db_field']
            transform = template_item.get('transform')

            # Get the answer value
            if not db_field:
                # Empty row or section header
                answer = ""
            else:
                answer = await self._get_field_value(client_data, db_field, transform)

            questionnaire_data.append((question, answer))

        # Add additional sections
        questionnaire_data.extend(await self._generate_shareholders_section(client_data))
        questionnaire_data.extend(await self._generate_directors_section(client_data))
        questionnaire_data.extend(await self._generate_officers_section(client_data))
        questionnaire_data.extend(await self._generate_bank_account_section(client_data))
        questionnaire_data.extend(await self._generate_financial_section(client_data))

        return questionnaire_data

    async def _get_field_value(self, client_data: Dict[str, Any], db_field: str, transform: Optional[str] = None) -> str:
        """
        Extract field value from client data with optional transformation.

        Args:
            client_data: Client data dictionary
            db_field: Dot-notation field path (e.g., 'primary_registration.state_of_incorporation')
            transform: Optional transformation function name

        Returns:
            String value for the field
        """
        try:
            # Handle nested field access
            value = client_data
            for field_part in db_field.split('.'):
                if field_part.isdigit():
                    # Array index access
                    index = int(field_part)
                    if isinstance(value, list) and len(value) > index:
                        value = value[index]
                    else:
                        value = None
                        break
                else:
                    # Dictionary key access
                    value = value.get(field_part) if isinstance(value, dict) else None
                    if value is None:
                        break

            # Apply transformation if specified
            if transform and value is not None:
                value = await self._apply_transform(value, transform, client_data)

            # Convert to string and handle None values
            if value is None:
                return ""
            elif isinstance(value, bool):
                return "Yes" if value else "No"
            elif isinstance(value, datetime.datetime):
                return value.strftime('%m/%d/%Y')
            elif isinstance(value, (int, float)):
                return str(value)
            else:
                return str(value)

        except Exception as e:
            logger.warning(f"Error getting field value for {db_field}: {str(e)}")
            return ""

    async def _apply_transform(self, value: Any, transform: str, client_data: Dict[str, Any]) -> Any:
        """
        Apply transformation functions to field values.

        Args:
            value: The raw field value
            transform: The transformation function name
            client_data: Full client data for context

        Returns:
            Transformed value
        """
        if transform == "has_secondary_registration":
            return "Yes" if value and len(value) > 0 else "No"

        elif transform == "mailing_address":
            # Find mailing address from addresses list
            if isinstance(value, list):
                for addr in value:
                    if addr.get('address_type') == 'mailing' or addr.get('address_type') == 'registered':
                        address_obj = addr.get('address', {})
                        if isinstance(address_obj, dict):
                            parts = [
                                address_obj.get('street', ''),
                                address_obj.get('city', ''),
                                address_obj.get('state', ''),
                                address_obj.get('zip', '')
                            ]
                            return ', '.join([p for p in parts if p])
            return ""

        elif transform == "total_authorized_shares":
            if isinstance(value, list):
                return sum(share.get('stock_authorized', 0) for share in value)
            return ""

        elif transform == "total_issued_shares":
            if isinstance(value, list):
                return sum(share.get('stock_issued', 0) for share in value)
            return ""

        elif transform == "total_assets":
            # This would need to be calculated from financial data
            return ""

        elif transform == "shareholder_details":
            # Generate shareholder summary
            shareholders = client_data.get('shareholders', [])
            if shareholders:
                details = []
                for sh in shareholders:
                    person = sh.get('person', {})
                    name = f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()
                    ownership = sh.get('ownership', '')
                    if name and ownership:
                        details.append(f"{name} - {ownership}")
                return '; '.join(details)
            return ""

        elif transform == "filed_prior_returns":
            # Check if there are any filed tax reports
            tax_reports = client_data.get('tax_reports', [])
            return "Yes" if any(report.get('status') == 'FILED' for report in tax_reports) else "No"

        elif transform == "we_prepared_returns":
            # Check if any tax reports were filed by us
            tax_reports = client_data.get('tax_reports', [])
            return "Yes" if any(report.get('filed_by') == 'us' for report in tax_reports) else "No"

        elif transform == "filed_1099":
            # Check if 1099 forms were filed
            return "Yes" if "1099" in str(value).upper() else "No"

        elif transform == "yes_no":
            return "Yes" if value else "No"

        return value

    async def _generate_shareholders_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the shareholders/owners section."""
        section_data = [
            ("", ""),  # Empty row
            ("II. Shareholders/Owners", ""),
        ]

        shareholders = client_data.get('shareholders', [])

        for i, shareholder in enumerate(shareholders[:4], 1):  # Limit to 4 shareholders
            person = shareholder.get('person', {})

            section_data.extend([
                (f"Name {i}", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Citizenship", person.get('citizenship', '')),
                ("Address of Residence", person.get('address', '')),
                ("% of ownership", shareholder.get('ownership', '')),
                ("", person.get('phone', '')),  # Phone number in empty field
            ])

        # Fill remaining slots if less than 4 shareholders
        for i in range(len(shareholders) + 1, 5):
            section_data.extend([
                (f"Name {i}", ""),
                ("Citizenship", ""),
                ("Address of Residence", ""),
                ("% of ownership", ""),
                ("", ""),  # Empty row
            ])

        return section_data

    async def _generate_directors_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the directors section."""
        section_data = [
            ("", ""),  # Empty row
            ("III. Director(s) (at least one)", ""),
        ]

        # Get directors from contacts (assuming directors have position containing 'director')
        contacts = client_data.get('contacts', [])
        directors = [c for c in contacts if 'director' in str(c.get('position', '')).lower()]

        for i, director in enumerate(directors[:4], 1):  # Limit to 4 directors
            person = director.get('person', {})

            section_data.extend([
                (f"Name {i}", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Аddress", person.get('address', '')),
                ("Phone number", person.get('phone', '') or director.get('phone', '')),
                ("", ""),  # Empty row
            ])

        # Fill remaining slots if less than 4 directors
        for i in range(len(directors) + 1, 5):
            section_data.extend([
                (f"Name {i}", ""),
                ("Аddress", ""),
                ("Phone number", ""),
                ("", ""),  # Empty row
            ])

        return section_data

    async def _generate_officers_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the officers section."""
        section_data = [
            ("", ""),  # Empty row
            ("IV. Officer(s) (at least one, who will sign declaration)", ""),
        ]

        # Get officers from contacts (assuming officers have position containing 'ceo', 'president', etc.)
        contacts = client_data.get('contacts', [])
        officers = [c for c in contacts if any(title in str(c.get('position', '')).lower()
                                             for title in ['ceo', 'president', 'officer', 'cfo', 'cto'])]

        if officers:
            officer = officers[0]  # Take the first officer as CEO
            person = officer.get('person', {})

            section_data.extend([
                ("CEO - name", f"{person.get('firstname', '')} {person.get('lastname', '')}".strip()),
                ("Аddress", person.get('address', '')),
                ("Phone number", person.get('phone', '') or officer.get('phone', '')),
            ])
        else:
            section_data.extend([
                ("CEO - name", ""),
                ("Аddress", ""),
                ("Phone number", ""),
            ])

        return section_data

    async def _generate_bank_account_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the bank account information section."""
        section_data = [
            ("", ""),  # Empty row
            ("V. BANK ACCOUNT INFORMATION for TAX WITHDRAWALS BY IRS", ""),
        ]

        bank_accounts = client_data.get('bank_accounts', [])

        if bank_accounts:
            account = bank_accounts[0]  # Take the first bank account

            section_data.extend([
                ("account name (if different from company name above)", ""),
                ("bank name", account.get('bank_name', '')),
                ("ABA for ACH witdrawal", account.get('aba_number', '')),
                ("account number", account.get('account_number', '')),
                ("", str(datetime.datetime.now().year)),  # Current year
            ])
        else:
            section_data.extend([
                ("account name (if different from company name above)", ""),
                ("bank name", ""),
                ("ABA for ACH witdrawal", ""),
                ("account number", ""),
                ("", str(datetime.datetime.now().year)),  # Current year
            ])

        return section_data

    async def _generate_financial_section(self, client_data: Dict[str, Any]) -> List[Tuple[str, str]]:
        """Generate the financial transactions and profit/loss section."""
        section_data = [
            ("VI. ЕСЛИ БЫЛИ ФИНАНСОВЫЕ ТРАНСАКЦИИ МЕЖДУ КОРПОРАЦИЕЙ И ВЛАДЕЛЬЦАМИ-НЕРЕЗИДЕНТАМИ, необходимо подать форму 5472", ""),
            ("Сумма и назначение переводов ОТ КОМПАНИИ ВЛАДЕЛЬЦУ (имя:)", ""),
            ("Сумма и назначение переводов ОТ ВЛАДЕЛЬЦА (имя:) в КОМПАНИЮ", ""),
            ("", ""),  # Empty row
            ("", "$$$"),
            ("VII. Profit and Loss - если бухгалтерия ведется в QuickBooks, достаточно приложить отчеты сгенерированные прогрраммой.", ""),
            ("Total Revenue (общий доход, без учета расходов)", "0"),
            ("Total Expenses (общая сумма расходов - приложите таблицу расходов)", "0"),
            ("Gross Profit (Прибыль - Доход минус расход)", "0"),
            ("Balance at the beginning of the year \n(Сумма на счету на начало года)", "0"),
            ("Balance at the end of the year \n(сумма на счету на конец года)", "0"),
            ("Loans (займы, взятые в банке или предоставленные акционерами - приложите расшифровку; если акции не распределены официально все вложения акционеров рассматриваются как \"КРЕДИТ ОТ АКЦИОНЕРА\")", "0"),
            ("", ""),  # Empty row
            ("Owners Equity (investments/stock sales) (инвестиции / продажа акций, зафиксированные документально )", "0"),
            ("Bank Accounts outside of US, if any \n(Если есть банковские счета и активность за пределами США,  укажите Наименование банка, адрес банка, номер счета, максимальный баланс на счету за прошлый год)", "No"),
            ("", ""),  # Empty row
            ("If a Corporation is registered in California, was FTB tax paid? Please, incude receipt / proof of payment \nЕсли Корпорация зарегистрирована в Калифорнии, оплачивался ли минимальный налог штата ? Приложите инвойса или чека (квитанции на оплату)", "No"),
        ]

        return section_data
