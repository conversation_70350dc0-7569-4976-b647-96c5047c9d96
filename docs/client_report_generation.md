# Client Report Generation Feature

## Overview

The client report generation feature creates professional questionnaire-style reports for clients based on the structure and format found in the reference Excel file `Questionnaire_CORP_2024-05-07_AISTATS.xlsx`. The system generates reports in multiple formats (Excel, CSV, and PDF) and is designed to be flexible and adaptable for different clients.

## Architecture

### Components

1. **API Endpoint**: `records/api/client_reports.py`
   - `download_main_report()` - Main entry point for report generation
   - Supports multiple formats: `xlsx`, `csv`, `pdf`
   - Supports different report types: `questionnaire`, `summary`

2. **Service Layer**: `records/services/client_report_generator.py`
   - `ClientReportGenerator` - Core service class
   - Handles data transformation and report generation
   - Manages questionnaire template structure

3. **Database Integration**: Uses existing `get_client_data` function
   - Leverages the centralized client data aggregation from `records.services.clients`
   - Retrieves comprehensive client data from multiple tables in a single call
   - Handles missing data gracefully with fallback values

## Features

### Report Formats

- **Excel (.xlsx)**: Professional formatted spreadsheet with styling
- **CSV (.csv)**: Plain text format for data processing
- **PDF (.pdf)**: Print-ready format (planned for future implementation)

### Report Sections

The questionnaire report includes the following sections:

1. **Corporation INFO**
   - Company details (name, EIN, description, NAICS code)
   - Registration information (primary and secondary states)
   - Business details (accounting method, financial year end)
   - Contact information (address, phone)
   - Subsidiary information
   - Share structure and assets

2. **Shareholders/Owners**
   - Up to 4 shareholders with details:
     - Name, citizenship, address, ownership percentage, phone

3. **Directors**
   - Up to 4 directors with details:
     - Name, address, phone number

4. **Officers**
   - CEO and other officers with details:
     - Name, address, phone number

5. **Bank Account Information**
   - Account details for tax withdrawals:
     - Account name, bank name, ABA number, account number

6. **Financial Information**
   - Financial transactions between corporation and owners
   - Profit and loss information
   - Balance information
   - Loans and equity information
   - International bank accounts
   - State tax compliance

## Usage

### API Endpoint

```http
GET /api/reports/download_main_report?client_id={client_id}&format={format}&report_type={report_type}
```

**Parameters:**
- `client_id` (required): The ID of the client to generate the report for
- `format` (optional): Output format - `xlsx` (default), `csv`, or `pdf`
- `report_type` (optional): Report type - `questionnaire` (default) or `summary`

**Response:**
- Returns a streaming response with the generated report file
- Content-Type varies based on format
- Includes Content-Disposition header with filename

### Example Usage

```python
# Generate Excel questionnaire report
response = await download_main_report(
    client_id="12345",
    format="xlsx",
    report_type="questionnaire"
)

# Generate CSV report
response = await download_main_report(
    client_id="12345",
    format="csv",
    report_type="questionnaire"
)
```

### Programmatic Usage

```python
from records.services.client_report_generator import ClientReportGenerator

# Create generator instance
generator = ClientReportGenerator()

# Generate Excel report
excel_data, filename = await generator.generate_questionnaire_excel(client_id)

# Generate CSV report
csv_data, filename = await generator.generate_questionnaire_csv(client_id)
```

## Data Mapping

The system maps database fields to questionnaire fields using a flexible template system:

### Database Tables Used

- `clients` - Main client information
- `client_addresses` - Client addresses with address details
- `client_contacts` - Directors and officers information
- `client_llc_shareholders` - Shareholder information
- `client_registrations` - Primary and secondary state registrations
- `bank_accounts` - Bank account information
- `client_shares` - Share structure information
- `client_tax_reports` - Tax filing information

### Field Transformations

The system includes several transformation functions:

- `has_secondary_registration` - Converts list to Yes/No
- `mailing_address` - Formats address components
- `total_authorized_shares` - Sums authorized shares
- `total_issued_shares` - Sums issued shares
- `shareholder_details` - Formats shareholder summary
- `filed_prior_returns` - Checks tax filing status
- `yes_no` - Converts boolean to Yes/No

## Configuration

### Template Structure

The questionnaire template is defined in `ClientReportGenerator._get_questionnaire_template()` and includes:

```python
{
    "section": "Section name",
    "field": "Question text",
    "db_field": "database.field.path",
    "required": True/False,
    "transform": "transformation_function_name"
}
```

### Customization

To customize the report:

1. **Add new fields**: Extend the template in `_get_questionnaire_template()`
2. **Add transformations**: Implement new functions in `_apply_transform()`
3. **Modify sections**: Update section generation methods
4. **Change styling**: Modify Excel styling in `generate_questionnaire_excel()`

## Error Handling

The system includes comprehensive error handling:

- **Missing client**: Returns 404 HTTP error
- **Database errors**: Logs warnings and continues with empty values
- **Missing data**: Gracefully handles missing fields with default values
- **Format errors**: Returns 400 HTTP error for unsupported formats

## Dependencies

Required Python packages:
- `openpyxl>=3.1.0` - Excel file generation
- `pandas>=1.5.3` - Data manipulation
- `fastapi>=0.100.0` - API framework

## Testing

Use the provided test script to verify functionality:

```bash
python3 test_client_report.py
```

The test script:
- Validates template structure
- Tests report generation (with real client ID)
- Saves sample output files for inspection

## Future Enhancements

1. **PDF Generation**: Implement PDF report generation using reportlab or weasyprint
2. **Custom Templates**: Allow users to create custom report templates
3. **Batch Processing**: Generate reports for multiple clients
4. **Scheduling**: Automated report generation and delivery
5. **Internationalization**: Support for multiple languages
6. **Advanced Styling**: More sophisticated Excel formatting options

## Troubleshooting

### Common Issues

1. **Missing openpyxl**: Install with `pip install openpyxl>=3.1.0`
2. **Client not found**: Verify client ID exists in database
3. **Empty reports**: Check if client has associated data in related tables
4. **Format errors**: Ensure format parameter is one of: xlsx, csv, pdf

### Debugging

Enable debug logging to see detailed information:

```python
import logging
logging.getLogger('records.services.client_report_generator').setLevel(logging.DEBUG)
```

## Security Considerations

- Validate client ID to prevent unauthorized access
- Implement proper authentication and authorization
- Sanitize file names to prevent path traversal attacks
- Limit report size to prevent memory exhaustion
- Rate limiting for report generation endpoints
